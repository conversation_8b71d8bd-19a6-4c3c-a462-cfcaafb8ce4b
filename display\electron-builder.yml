appId: com.electron.app
productName: display
directories:
  buildResources: build
  output: release
files:
  - "out/**/*"
  - "package.json"
  - "resources/**/*"
  - "!**/.vscode/*"
  - "!src/*"
  - "!node_modules/*"
  - "!dist/*"
  - "!dist_old/*"
  - "!build-output/*"
  - "!Olllll/*"
  - "!public/*"
  - "!encrypted-assets/*"
  - "!scripts/*"
  - "!electron.vite.config.{js,ts,mjs,cjs}"
  - "!{.eslintcache,eslint.config.mjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}"
  - "!{.env,.env.*,.npmrc,pnpm-lock.yaml}"
  - "!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}"
  - "!tailwind.config.js"
  - "!**/*.zip"
  - "!**/*.rar"
  - "!**/*.stackdump"

  - "!nul"
asar: false
win:
  executableName: display
nsis:
  artifactName: ${name}-${version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
mac:
  entitlementsInherit: build/entitlements.mac.plist
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
dmg:
  artifactName: ${name}-${version}.${ext}
linux:
  target:
    - AppImage
    - snap
    - deb
  maintainer: electronjs.org
  category: Utility
appImage:
  artifactName: ${name}-${version}.${ext}
npmRebuild: false
publish:
  provider: generic
  url: https://example.com/auto-updates
